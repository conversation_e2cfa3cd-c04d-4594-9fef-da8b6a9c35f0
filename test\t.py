#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 路径执行程序 (流式队列版) - v16

策略: 流式发送 + 解耦控制 + 缓冲稳定

最终修正:
- 彻底放弃批次间等待，采用流式发送，最大化发挥队列模式的平滑优势。
- 一次性生成所有运动指令，然后连续推送到控制器队列，仅在队列满时发送。
- 整个G-code路径只进行一次最终等待，消除所有中间停顿和震动。
"""

import sys
import os
import time
import re
import math
import numpy as np
from typing import List, Dict

try:
    from config import ROBOT_IP, ROBOT_PORT
    import nrc_interface as nrc
except ImportError:
    print("❌ 错误: 无法导入 nrc_interface 或 config。请确保文件路径正确。")
    sys.exit(1)

class GCodeStreamer:
    def __init__(self, ip: str, port: str, config: Dict):
        self.ip = ip; self.port = port; self.socket_fd = -1
        self.config = config
        self.last_pose_state = {
            'point': {'x': 0, 'y': 0, 'z': 0},
            'pose_deg': self.config.get("G0_FIXED_POSE_DEG")
        }

    @staticmethod
    def normalize_angle(angle: float) -> float:
        while angle > 180: angle -= 360
        while angle <= -180: angle += 360
        return angle

    def parse_gcode_to_segments(self) -> List[Dict]:
        filepath = self.config.get("GCODE_FILE")
        print(f"📄 正在解析G-code并分段: {filepath}")
        segments = []
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                # ... (解析逻辑与之前版本相同) ...
                coord_regex = re.compile(r'([XYZABC])([-+]?\d*\.?\d+)')
                for line in f:
                    line = line.strip().upper()
                    cmd_type = 'G0' if line.startswith('G0') else 'G1' if line.startswith('G1') else None
                    if cmd_type:
                        coords = dict(coord_regex.findall(line))
                        if all(k in coords for k in ('X', 'Y', 'Z')):
                            point = {'x': float(coords.get('X')),'y': float(coords.get('Y')),'z': float(coords.get('Z')),'a': float(coords.get('A')) if 'A' in coords else None,'b': float(coords.get('B')) if 'B' in coords else None,'c': float(coords.get('C')) if 'C' in coords else None}
                            if not segments or segments[-1]['type'] != cmd_type:
                                segments.append({'type': cmd_type, 'points': [point]})
                            else:
                                segments[-1]['points'].append(point)
            print(f"✅ 解析完成，共找到 {len(segments)} 个运动段。")
            return segments
        except FileNotFoundError:
            print(f"❌ 错误：G-code文件未找到: {filepath}"); return []

    def connect_and_initialize(self) -> bool:
        print(f"🔗 正在连接机械臂 {self.ip}:{self.port}...")
        self.socket_fd = nrc.connect_robot(self.ip, str(self.port))
        if self.socket_fd <= 0: print("❌ 连接失败！"); return False
        print(f"✅ 连接成功！")
        
        print("\n🔧 正在初始化机器人...")
        if not self._power_on(): return False
        if nrc.set_user_coord_number(self.socket_fd, self.config.get("USER_COORD_NUMBER")) != 0:
            print(f"❌ 设置用户坐标系失败"); return False
        
        print("\n🔄 正在设置执行模式和速度...")
        try:
            if nrc.set_current_mode(self.socket_fd, 1) != 0: print("❌ 切换到远程模式失败"); return False; time.sleep(0.5)
            if nrc.set_speed(self.socket_fd, 100) != 0: print("❌ 设置全局速度失败"); return False; time.sleep(0.5)
            if nrc.queue_motion_set_status(self.socket_fd, True) != 0: print("❌ 启用队列模式失败"); return False
            print("✅ 机器人已准备就绪。")
            return True
        except Exception as e:
            print(f"❌ 准备执行时发生错误: {e}"); return False

    def _power_on(self) -> bool:
        # ... (与之前版本相同) ...
        try:
            res = nrc.get_servo_state(self.socket_fd, 0)
            if isinstance(res, list) and res[1] == 3: print("✅ 机器人伺服已上电。"); return True
            print("ℹ️ 机器人需要上电..."); nrc.clear_error(self.socket_fd); time.sleep(0.5)
            nrc.set_servo_state(self.socket_fd, 1); time.sleep(0.5)
            if nrc.set_servo_poweron(self.socket_fd) != 0: print(f"❌ 上电失败！"); return False
            print("⏳ 等待上电稳定..."); time.sleep(2.0)
            res = nrc.get_servo_state(self.socket_fd, 0)
            if isinstance(res, list) and res[1] == 3: print("✅ 机器人上电成功！"); return True
            print(f"❌ 上电后状态异常: {res}"); return False
        except Exception as e:
            print(f"❌ 上电过程失败: {e}"); return False

    def process_and_stream_gcode(self, segments: List[Dict]):
        print("\n" + "=" * 40); print(f"🚀 开始生成并流式发送G-code指令..."); print("=" * 40)
        
        # 1. 一次性生成所有指令
        all_commands = self._generate_all_commands(segments)
        if not all_commands:
            print("⚠️ 未生成任何有效指令。"); return
        
        print(f"✅ 已生成 {len(all_commands)} 条指令，准备推送到控制器队列...")

        # 2. 流式推送到队列
        queue_limit = self.config.get("CONTROLLER_QUEUE_LIMIT")
        queue_count = 0
        nrc.queue_motion_clear_Data(self.socket_fd)

        for i, cmd in enumerate(all_commands):
            if nrc.queue_motion_push_back_moveL(self.socket_fd, cmd) != 0:
                raise RuntimeError(f"推送第 {i+1} 条指令失败")
            queue_count += 1
            
            # 如果队列满了，或者这是最后一条指令，就发送
            if queue_count >= queue_limit or (i == len(all_commands) - 1):
                print(f"  - 队列达到 {queue_count} 条，发送批次...")
                if nrc.queue_motion_send_to_controller(self.socket_fd, queue_count) != 0:
                    raise RuntimeError("发送批次失败")
                queue_count = 0 # 重置计数器
                # 注意：这里没有等待！
        
        # 3. 所有指令推送并发送完毕后，进行唯一一次等待
        print("\n✅ 所有指令已发送，等待整体路径执行完成...")
        if not self._wait_for_completion():
            raise RuntimeError("整体路径执行超时或失败")

    def _generate_all_commands(self, segments: List[Dict]) -> List:
        all_commands = []
        
        # 获取真实初始状态
        pos_vec = nrc.VectorDouble()
        if nrc.get_current_position(self.socket_fd, self.config.get("USER_COORD_NUMBER"), pos_vec) == 0 and len(pos_vec) >= 6:
             point = {'x':pos_vec[0], 'y':pos_vec[1], 'z':pos_vec[2]}
             pose_deg = [self.normalize_angle(math.degrees(a)) for a in pos_vec[3:6]]
             self.last_pose_state = {'point': point, 'pose_deg': pose_deg}
        print(f"  初始位置状态已记录: pose = {['{:.1f}'.format(p) for p in self.last_pose_state['pose_deg']]}°")

        for segment in segments:
            is_g0 = segment['type'] == 'G0'
            vel = self.config.get("G0_VELOCITY_PERCENT") if is_g0 else self.config.get("G1_VELOCITY_PERCENT")
            smooth = self.config.get("G0_SMOOTHING") if is_g0 else self.config.get("G1_SMOOTHING")

            for point in segment['points']:
                target_pos_xyz = [point['x'], point['y'], point['z']]
                
                if is_g0:
                    target_pose_deg = self.config.get("G0_FIXED_POSE_DEG")
                else:
                    target_pose_deg = self._get_g1_pose_deg(point)

                # 检查姿态变化
                pose_change = np.abs(np.array(target_pose_deg) - np.array(self.last_pose_state['pose_deg']))
                for i in range(3):
                    if pose_change[i] > 180: pose_change[i] = 360 - pose_change[i]
                
                if np.any(pose_change > self.config.get("POSE_CHANGE_THRESHOLD_DEG")):
                    # 先移动
                    cmd_move = self._create_command(target_pos_xyz, self.last_pose_state['pose_deg'], vel, self.config.get("ACCEL_PERCENT"), smooth)
                    all_commands.append(cmd_move)
                    # 后旋转
                    cmd_rotate = self._create_command(target_pos_xyz, target_pose_deg, vel, self.config.get("ACCEL_PERCENT"), smooth)
                    all_commands.append(cmd_rotate)
                else:
                    cmd_normal = self._create_command(target_pos_xyz, target_pose_deg, vel, self.config.get("ACCEL_PERCENT"), smooth)
                    all_commands.append(cmd_normal)
                
                self.last_pose_state = {'point': point, 'pose_deg': target_pose_deg}
        
        return all_commands

    def _get_g1_pose_deg(self, point: Dict) -> List[float]:
        # (与之前版本相同)
        last_pose = self.last_pose_state['pose_deg']
        offset_a, offset_b, offset_c = self.config.get("GCODE_TO_ROBOT_OFFSET_A"), self.config.get("GCODE_TO_ROBOT_OFFSET_B"), self.config.get("GCODE_TO_ROBOT_OFFSET_C")
        last_gcode_a = last_pose[0] - offset_a
        last_gcode_b = last_pose[1] - offset_b
        last_gcode_c = last_pose[2] - offset_c
        gcode_a = point['a'] if point['a'] is not None else last_gcode_a
        gcode_b = point['b'] if point['b'] is not None else last_gcode_b
        gcode_c = point['c'] if point['c'] is not None else last_gcode_c
        rx = self.normalize_angle(gcode_a + offset_a)
        ry = self.normalize_angle(gcode_b + offset_b)
        rz = self.normalize_angle(gcode_c + offset_c)
        return [rx, ry, rz]

    def _create_command(self, pos_xyz, pose_deg, vel, accel, smooth):
        # (与之前版本相同)
        pose_rad = [math.radians(a) for a in pose_deg]
        cmd = nrc.MoveCmd(); cmd.targetPosType = nrc.PosType_data
        cmd.targetPosValue = nrc.VectorDouble()
        for val in pos_xyz + pose_rad: cmd.targetPosValue.append(val)
        cmd.coord = 3; cmd.userNum = self.config.get("USER_COORD_NUMBER")
        cmd.velocity = vel; cmd.acc = accel; cmd.dec = accel; cmd.pl = smooth
        return cmd

    def _wait_for_completion(self, timeout=300) -> bool:
        # (与之前版本相同)
        print(f"      ⏳ 等待完成...", end="", flush=True)
        start = time.time()
        while time.time() - start < timeout:
            try:
                state_res = nrc.get_robot_running_state(self.socket_fd, 0)
                len_res = nrc.queue_motion_get_queuelen(self.socket_fd, 0)
                is_stopped = isinstance(state_res, list) and state_res[1] == 0
                is_empty = isinstance(len_res, list) and len_res[1] == 0
                if is_stopped and is_empty: print(" ✅"); return True
                time.sleep(0.2); print(".", end="", flush=True)
            except Exception: time.sleep(1)
        print(" ❌ 超时!"); return False

    def shutdown(self):
        # (与之前版本相同)
        if self.socket_fd > 0:
            print("\n程序结束，正在安全关闭...")
            nrc.queue_motion_set_status(self.socket_fd, False); time.sleep(0.2)
            nrc.queue_motion_clear_Data(self.socket_fd); time.sleep(0.2)
            print("🔄 正在切换回示教模式...")
            if nrc.set_current_mode(self.socket_fd, 0) == 0:
                print("✅ 已成功切换回示教模式。")
            else:
                print("⚠️ 切换回示教模式失败。")
            time.sleep(0.5)
            self._power_off()
            print("🔌 正在断开连接..."); nrc.disconnect_robot(self.socket_fd); print("✅ 连接已断开。")

    def _power_off(self):
        print("\nℹ️ 正在安全下电...")
        try:
            nrc.set_servo_poweroff(self.socket_fd); time.sleep(1.5); print("✅ 机器人已下电。")
        except Exception as e:
            print(f"❌ 下电过程失败: {e}")

def main():
    config = {
        "GCODE_FILE": "jiyi.Gcode", "USER_COORD_NUMBER": 1,
        "G0_VELOCITY_PERCENT": 80, "G1_VELOCITY_PERCENT": 30, "ACCEL_PERCENT": 40,
        "G0_SMOOTHING": 2, "G1_SMOOTHING": 5, # 启用平滑以获得最佳效果
        "G0_FIXED_POSE_DEG": [180.0, 0.0, 0.0],
        "GCODE_TO_ROBOT_OFFSET_A": 180.0, "GCODE_TO_ROBOT_OFFSET_B": 0.0, "GCODE_TO_ROBOT_OFFSET_C": 0.0,
        "POSE_CHANGE_THRESHOLD_DEG": 15.0,
        "CONTROLLER_QUEUE_LIMIT": 20,
    }
    processor = GCodeStreamer(ROBOT_IP, ROBOT_PORT, config)
    try:
        segments = processor.parse_gcode_to_segments()
        if not segments: return
        if not processor.connect_and_initialize(): return
        
        processor.process_and_stream_gcode(segments)

        print("\n" + "=" * 40); print("🎉 所有G-code路径执行完毕！"); print("=" * 40)
    except (ConnectionError, RuntimeError, Exception) as e:
        print(f"\n❌ 在主程序中发生严重错误: {e}")
    finally:
        processor.shutdown()

if __name__ == "__main__":
    main()